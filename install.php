<?php
defined('BASEPATH') or exit('No direct script access allowed');

// Facebook Leads Integration Module Installation
// This module is now free to use without license validation

$CI = &get_instance();

// Set default options for the module
if (!get_option('appId')) {
    add_option('appId', '');
}

if (!get_option('appSecret')) {
    add_option('appSecret', '');
}

if (!get_option('verifytoken')) {
    add_option('verifytoken', 'token654321');
}

if (!get_option('subscribed_pages')) {
    add_option('subscribed_pages', json_encode(array()));
}

if (!get_option('facebook_pages')) {
    add_option('facebook_pages', json_encode(array()));
}

if (!get_option('longLifeAccessToken')) {
    add_option('longLifeAccessToken', '');
}

if (!get_option('facebook_lead_assigned')) {
    add_option('facebook_lead_assigned', '');
}

if (!get_option('facebook_lead_source')) {
    add_option('facebook_lead_source', '');
}

if (!get_option('facebook_lead_status')) {
    add_option('facebook_lead_status', '');
}

// Mark module as activated without license validation
add_option('facebookleadsintegration_activated', '1');
add_option('facebookleadsintegration_free_version', '1');

?>