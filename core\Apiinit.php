<?php

namespace modules\facebookleadsintegration\core;

// License validation dependencies removed - module is now free to use
// require_once __DIR__.'/../third_party/node.php';
// require_once __DIR__.'/../vendor/autoload.php';
// use Firebase\JWT\JWT as Facebookleadsintegration_JWT;
// use Firebase\JWT\Key as Facebookleadsintegration_Key;
// use WpOrg\Requests\Requests as Facebookleadsintegration_Requests;

class Apiinit
{
    public static function the_da_vinci_code($module_name)
    {
        // License validation bypassed - always return true
        return true;
    }


    public static function ease_of_mind($module_name)
    {
        // License validation bypassed - always return true
        return true;
    }


    public static function activate($module)
    {
        // License validation bypassed - module activates without verification
        return true;
    }

    
    public static function getUserIP()
    {
        $ipaddress = '';
        if (isset($_SERVER['HTTP_CLIENT_IP'])) {
            $ipaddress = $_SERVER['HTTP_CLIENT_IP'];
        } elseif (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            $ipaddress = $_SERVER['HTTP_X_FORWARDED_FOR'];
        } elseif (isset($_SERVER['HTTP_X_FORWARDED'])) {
            $ipaddress = $_SERVER['HTTP_X_FORWARDED'];
        } elseif (isset($_SERVER['HTTP_FORWARDED_FOR'])) {
            $ipaddress = $_SERVER['HTTP_FORWARDED_FOR'];
        } elseif (isset($_SERVER['HTTP_FORWARDED'])) {
            $ipaddress = $_SERVER['HTTP_FORWARDED'];
        } elseif (isset($_SERVER['REMOTE_ADDR'])) {
            $ipaddress = $_SERVER['REMOTE_ADDR'];
        } else {
            $ipaddress = 'UNKNOWN';
        }

        return $ipaddress;
    }


    public static function pre_validate($module_name, $code = '')
    {
        // License validation bypassed - always return success
        return ['status' => true, 'message' => 'Module activated successfully'];
    }
}
