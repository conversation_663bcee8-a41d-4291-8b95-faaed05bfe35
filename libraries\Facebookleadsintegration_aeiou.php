<?php

defined('BASEPATH') || exit('No direct script access allowed');
// License validation dependencies removed - module is now free to use
// require_once __DIR__.'/../vendor/autoload.php';
// require_once __DIR__.'/../third_party/node.php';
// use Firebase\JWT\JWT as Facebookleadsintegration_JWT;
// use Firebase\JWT\Key as Facebookleadsintegration_Key;
// use WpOrg\Requests\Requests as Facebookleadsintegration_Requests;

class Facebookleadsintegration_aeiou
{
    public static function getPurchaseData($code)
    {
        // License validation bypassed - return mock valid data
        return (object) [
            'sold_at' => date('Y-m-d H:i:s'),
            'supported_until' => date('Y-m-d H:i:s', strtotime('+1 year')),
            'item' => (object) ['id' => '25623248']
        ];
    }

    public static function verifyPurchase($code)
    {
        // License validation bypassed - always return null (valid)
        return null;
    }

    public function validatePurchase($module_name)
    {
        // License validation bypassed - always return true
        return true;
    }
}
